'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Plus, Search, Edit, Trash2, Building2 } from 'lucide-react'
import { School } from '@/types'
import { adminApi } from '@/lib/api'
import { formatDate } from '@/lib/utils'
import EditSchoolForm from '@/components/admin/EditSchoolForm'

export default function SchoolsManagement() {
  const [schools, setSchools] = useState<School[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [showAddModal, setShowAddModal] = useState(false)
  const [editingSchool, setEditingSchool] = useState<School | null>(null)

  // 获取学校列表
  const fetchSchools = async () => {
    try {
      setLoading(true)
      const response = await adminApi.schools.getList({ 
        page: 1, 
        size: 100,
        keyword: searchTerm 
      })
      if (response.data.success) {
        setSchools(response.data.data.records || response.data.data)
      }
    } catch (error) {
      console.error('获取学校列表失败:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchSchools()
  }, [])

  // 过滤学校列表
  const filteredSchools = schools.filter(school =>
    school.schoolName.toLowerCase().includes(searchTerm.toLowerCase())
  )

  // 删除学校
  const handleDelete = async (id: number) => {
    if (confirm('确定要删除这所学校吗？此操作不可撤销。')) {
      try {
        const response = await adminApi.schools.delete(id)
        if (response.data.success) {
          alert('删除学校成功')
          fetchSchools()
        } else {
          alert(response.data.message || '删除学校失败')
        }
      } catch (error) {
        console.error('删除学校失败:', error)
        alert('删除学校失败')
      }
    }
  }

  // 保存编辑后的学校信息
  const handleSaveSchool = (updatedSchool: School) => {
    // 更新本地状态
    setSchools(prevSchools => 
      prevSchools.map(school => 
        school.id === updatedSchool.id ? updatedSchool : school
      )
    )
    setEditingSchool(null)
    // 显示成功消息
    alert('学校信息更新成功')
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">加载中...</div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 页面头部 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">学校管理</h1>
          <p className="text-gray-600 mt-1">管理系统中的所有学校信息</p>
        </div>
        <Button 
          onClick={() => setShowAddModal(true)}
          className="bg-blue-600 hover:bg-blue-700"
        >
          <Plus className="h-4 w-4 mr-2" />
          添加学校
        </Button>
      </div>

      {/* 搜索栏 */}
      <Card>
        <CardContent className="pt-6">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="搜索学校名称..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </CardContent>
      </Card>

      {/* 统计信息 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">学校总数</CardTitle>
            <Building2 className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{schools.length}</div>
            <p className="text-xs text-gray-500">已注册学校</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">学院总数</CardTitle>
            <Building2 className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {schools.reduce((sum, school) => sum + (school.collegeCount || 0), 0)}
            </div>
            <p className="text-xs text-gray-500">所有学校的学院数</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">搜索结果</CardTitle>
            <Search className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{filteredSchools.length}</div>
            <p className="text-xs text-gray-500">符合条件的学校</p>
          </CardContent>
        </Card>
      </div>

      {/* 学校列表 */}
      <Card>
        <CardHeader>
          <CardTitle>学校列表</CardTitle>
          <CardDescription>
            共 {filteredSchools.length} 所学校
          </CardDescription>
        </CardHeader>
        <CardContent>
          {filteredSchools.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              {searchTerm ? '没有找到匹配的学校' : '暂无学校数据'}
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-3 px-4 font-medium text-gray-900">学校名称</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">学院数量</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">创建时间</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">更新时间</th>
                    <th className="text-right py-3 px-4 font-medium text-gray-900">操作</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredSchools.map((school) => (
                    <tr key={school.id} className="border-b hover:bg-gray-50">
                      <td className="py-3 px-4">
                        <div className="font-medium text-gray-900">{school.schoolName}</div>
                      </td>
                      <td className="py-3 px-4 text-gray-600">
                        {school.collegeCount || 0} 个学院
                      </td>
                      <td className="py-3 px-4 text-gray-600">
                        {formatDate(school.createTime)}
                      </td>
                      <td className="py-3 px-4 text-gray-600">
                        {formatDate(school.updateTime)}
                      </td>
                      <td className="py-3 px-4">
                        <div className="flex items-center justify-end space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setEditingSchool(school)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDelete(school.id)}
                            className="text-red-600 hover:text-red-700 hover:border-red-300"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 添加学校模态框 - 这里先占位，后续实现 */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-medium mb-4">添加学校</h3>
            <p className="text-gray-600 mb-4">添加学校功能正在开发中...</p>
            <div className="flex justify-end space-x-2">
              <Button
                variant="outline"
                onClick={() => setShowAddModal(false)}
              >
                取消
              </Button>
              <Button onClick={() => setShowAddModal(false)}>
                确定
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* 编辑学校模态框 */}
      {editingSchool && (
        <EditSchoolForm
          school={editingSchool}
          onSave={handleSaveSchool}
          onCancel={() => setEditingSchool(null)}
        />
      )}
    </div>
  )
}