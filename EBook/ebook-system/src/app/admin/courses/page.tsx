'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Plus, Search, Edit, Trash2, BookOpen, GraduationCap } from 'lucide-react'
import { Course, Major, College, School } from '@/types'
import { courseApi, majorApi, collegeApi, schoolApi, adminApi } from '@/lib/api'
import EditCourseForm from '@/components/admin/EditCourseForm'

export default function CoursesManagement() {
  const [courses, setCourses] = useState<Course[]>([])
  const [majors, setMajors] = useState<Major[]>([])
  const [colleges, setColleges] = useState<College[]>([])
  const [schools, setSchools] = useState<School[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedMajor, setSelectedMajor] = useState<string>('')
  const [selectedCollege, setSelectedCollege] = useState<string>('')
  const [selectedSchool, setSelectedSchool] = useState<string>('')
  const [showAddModal, setShowAddModal] = useState(false)
  const [editingCourse, setEditingCourse] = useState<Course | null>(null)

  // 分页状态
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  const [total, setTotal] = useState(0)
  const [totalPages, setTotalPages] = useState(0)

  // 获取课程列表
  const fetchCourses = async (page = currentPage, size = pageSize, keyword = searchTerm, majorId = selectedMajor) => {
    try {
      setLoading(true)
      const params: any = { page, size }

      if (keyword) {
        params.keyword = keyword
      }
      if (majorId) {
        params.majorId = parseInt(majorId)
      }

      const response = await adminApi.courses.getList(params)
      if (response.data.success) {
        const pageData = response.data.data
        // 处理分页数据
        if (pageData.records) {
          // 分页格式
          setCourses(Array.isArray(pageData.records) ? pageData.records : [])
          setTotal(pageData.total || 0)
          setTotalPages(pageData.pages || 0)
          setCurrentPage(pageData.current || 1)
        } else {
          // 非分页格式
          setCourses(Array.isArray(pageData) ? pageData : [])
        }
      }
    } catch (error) {
      console.error('获取课程列表失败:', error)
      setCourses([]) // 确保设置为空数组
    } finally {
      setLoading(false)
    }
  }

  // 获取专业列表
  const fetchMajors = async () => {
    try {
      const response = await adminApi.majors.getList({ page: 1, size: 1000 })
      if (response.data.success) {
        const majorsData = response.data.data.records || response.data.data
        setMajors(Array.isArray(majorsData) ? majorsData : [])
      }
    } catch (error) {
      console.error('获取专业列表失败:', error)
      setMajors([])
    }
  }

  // 获取学院列表
  const fetchColleges = async () => {
    try {
      const response = await adminApi.colleges.getList({ page: 1, size: 1000 })
      if (response.data.success) {
        const collegesData = response.data.data.records || response.data.data
        setColleges(Array.isArray(collegesData) ? collegesData : [])
      }
    } catch (error) {
      console.error('获取学院列表失败:', error)
      setColleges([])
    }
  }

  // 获取学校列表
  const fetchSchools = async () => {
    try {
      const response = await adminApi.schools.getList({ page: 1, size: 1000 })
      if (response.data.success) {
        const schoolsData = response.data.data.records || response.data.data
        setSchools(Array.isArray(schoolsData) ? schoolsData : [])
      }
    } catch (error) {
      console.error('获取学校列表失败:', error)
      setSchools([])
    }
  }

  useEffect(() => {
    fetchMajors()
    fetchColleges()
    fetchSchools()
  }, [])

  useEffect(() => {
    fetchCourses()
  }, [currentPage, pageSize])

  // 搜索和筛选变化时重置到第一页
  useEffect(() => {
    if (currentPage !== 1) {
      setCurrentPage(1)
    } else {
      fetchCourses(1, pageSize, searchTerm, selectedMajor)
    }
  }, [searchTerm, selectedMajor])

  // 根据选择的学校和学院过滤专业
  const filteredMajors = Array.isArray(majors) ? majors.filter(major => {
    if (selectedCollege) {
      return major.collegeId.toString() === selectedCollege
    }
    if (selectedSchool) {
      const college = colleges.find(c => c.id === major.collegeId)
      return college && college.schoolId.toString() === selectedSchool
    }
    return true
  }) : []

  // 根据选择的学校过滤学院
  const filteredColleges = Array.isArray(colleges) ? (selectedSchool
    ? colleges.filter(college => college.schoolId.toString() === selectedSchool)
    : colleges) : []

  // 课程列表现在通过服务端分页，不需要客户端过滤
  const filteredCourses = Array.isArray(courses) ? courses : []

  // 分页处理函数
  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }

  const handlePageSizeChange = (size: number) => {
    setPageSize(size)
    setCurrentPage(1) // 重置到第一页
  }

  // 删除课程
  const handleDelete = async (id: number) => {
    if (confirm('确定要删除这门课程吗？此操作不可撤销。')) {
      try {
        const response = await adminApi.courses.delete(id)
        if (response.data.success) {
          // 成功删除后刷新列表
          fetchCourses()
        } else {
          alert(`删除失败: ${response.data.message}`)
        }
      } catch (error) {
        console.error('删除课程失败:', error)
        alert('删除课程时发生错误')
      }
    }
  }

  // 保存编辑后的课程信息
  const handleSaveCourse = (updatedCourse: Course) => {
    // 更新本地状态
    setCourses(prevCourses => 
      prevCourses.map(course => 
        course.id === updatedCourse.id ? updatedCourse : course
      )
    )
    setEditingCourse(null)
    // 显示成功消息
    alert('课程信息更新成功')
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">加载中...</div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 页面头部 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">课程管理</h1>
          <p className="text-gray-600 mt-1">管理系统中的所有课程信息</p>
        </div>
        <Button 
          onClick={() => setShowAddModal(true)}
          className="bg-blue-600 hover:bg-blue-700"
        >
          <Plus className="h-4 w-4 mr-2" />
          添加课程
        </Button>
      </div>

      {/* 搜索和筛选栏 */}
      <Card>
        <CardContent className="pt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="搜索课程名称或专业..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <select
              value={selectedSchool}
              onChange={(e) => {
                setSelectedSchool(e.target.value)
                setSelectedCollege('')
                setSelectedMajor('')
              }}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">所有学校</option>
              {schools.map((school) => (
                <option key={school.id} value={school.id.toString()}>
                  {school.schoolName}
                </option>
              ))}
            </select>
            <select
              value={selectedCollege}
              onChange={(e) => {
                setSelectedCollege(e.target.value)
                setSelectedMajor('')
              }}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">所有学院</option>
              {filteredColleges.map((college) => (
                <option key={college.id} value={college.id.toString()}>
                  {college.collegeName}
                </option>
              ))}
            </select>
            <select
              value={selectedMajor}
              onChange={(e) => setSelectedMajor(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">所有专业</option>
              {filteredMajors.map((major) => (
                <option key={major.id} value={major.id.toString()}>
                  {major.majorName}
                </option>
              ))}
            </select>
          </div>
        </CardContent>
      </Card>

      {/* 统计信息 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">课程总数</CardTitle>
            <BookOpen className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{total}</div>
            <p className="text-xs text-gray-500">课程总数</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">当前页</CardTitle>
            <GraduationCap className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{filteredCourses.length}</div>
            <p className="text-xs text-gray-500">本页课程数</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">有专业关联</CardTitle>
            <BookOpen className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Array.isArray(courses) ? courses.filter(course => course.majorId).length : 0}
            </div>
            <p className="text-xs text-gray-500">本页已关联专业</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">页码信息</CardTitle>
            <Search className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{currentPage}/{totalPages}</div>
            <p className="text-xs text-gray-500">当前页/总页数</p>
          </CardContent>
        </Card>
      </div>

      {/* 课程列表 */}
      <Card>
        <CardHeader>
          <CardTitle>课程列表</CardTitle>
          <CardDescription>
            共 {total} 门课程，当前第 {currentPage} 页，每页显示 {pageSize} 条
          </CardDescription>
        </CardHeader>
        <CardContent>
          {filteredCourses.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              {searchTerm || selectedMajor ? '没有找到匹配的课程' : '暂无课程数据'}
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-3 px-4 font-medium text-gray-900">课程名称</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">所属专业</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">参考资料</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">教材数量</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">创建时间</th>
                    <th className="text-right py-3 px-4 font-medium text-gray-900">操作</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredCourses.map((course) => (
                    <tr key={course.id} className="border-b hover:bg-gray-50">
                      <td className="py-3 px-4">
                        <div className="font-medium text-gray-900">{course.courseName}</div>
                      </td>
                      <td className="py-3 px-4 text-gray-600">
                        {course.majorName || '未关联专业'}
                      </td>
                      <td className="py-3 px-4 text-gray-600">
                        <div className="max-w-xs truncate">
                          {course.referenceMaterials || '暂无'}
                        </div>
                      </td>
                      <td className="py-3 px-4 text-gray-600">
                        {course.textbookCount || 0} 本教材
                      </td>
                      <td className="py-3 px-4 text-gray-600">
                        {new Date(course.createTime).toLocaleDateString()}
                      </td>
                      <td className="py-3 px-4">
                        <div className="flex items-center justify-end space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setEditingCourse(course)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDelete(course.id)}
                            className="text-red-600 hover:text-red-700 hover:border-red-300"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}

          {/* 分页控件 */}
          {total > 0 && (
            <div className="flex items-center justify-between mt-6 pt-6 border-t">
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-700">每页显示</span>
                <select
                  value={pageSize}
                  onChange={(e) => handlePageSizeChange(parseInt(e.target.value))}
                  className="px-3 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value={10}>10</option>
                  <option value={20}>20</option>
                  <option value={50}>50</option>
                  <option value={100}>100</option>
                </select>
                <span className="text-sm text-gray-700">条</span>
              </div>

              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage <= 1}
                >
                  上一页
                </Button>

                <div className="flex items-center space-x-1">
                  {/* 页码按钮 */}
                  {Array.from({ length: Math.min(totalPages, 7) }, (_, i) => {
                    let pageNum;
                    if (totalPages <= 7) {
                      pageNum = i + 1;
                    } else if (currentPage <= 4) {
                      pageNum = i + 1;
                    } else if (currentPage >= totalPages - 3) {
                      pageNum = totalPages - 6 + i;
                    } else {
                      pageNum = currentPage - 3 + i;
                    }

                    return (
                      <Button
                        key={pageNum}
                        variant={currentPage === pageNum ? "default" : "outline"}
                        size="sm"
                        onClick={() => handlePageChange(pageNum)}
                        className="w-8 h-8 p-0"
                      >
                        {pageNum}
                      </Button>
                    );
                  })}
                </div>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage >= totalPages}
                >
                  下一页
                </Button>
              </div>

              <div className="text-sm text-gray-700">
                共 {total} 条，第 {currentPage} / {totalPages} 页
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 添加课程模态框 - 占位 */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-medium mb-4">添加课程</h3>
            <p className="text-gray-600 mb-4">添加课程功能正在开发中...</p>
            <div className="flex justify-end space-x-2">
              <Button
                variant="outline"
                onClick={() => setShowAddModal(false)}
              >
                取消
              </Button>
              <Button onClick={() => setShowAddModal(false)}>
                确定
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* 编辑课程模态框 */}
      {editingCourse && (
        <EditCourseForm
          course={editingCourse}
          majors={majors}
          onSave={handleSaveCourse}
          onCancel={() => setEditingCourse(null)}
        />
      )}
    </div>
  )
}
