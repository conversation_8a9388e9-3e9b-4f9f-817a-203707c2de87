'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Plus, Search, Edit, Trash2, Building2, School } from 'lucide-react'
import { College, School as SchoolType } from '@/types'
import { collegeApi, schoolApi, adminApi } from '@/lib/api'
import { formatDate } from '@/lib/utils'
import EditCollegeForm from '@/components/admin/EditCollegeForm'

export default function CollegesManagement() {
  const [colleges, setColleges] = useState<College[]>([])
  const [schools, setSchools] = useState<SchoolType[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedSchool, setSelectedSchool] = useState<string>('')
  const [showAddModal, setShowAddModal] = useState(false)
  const [editingCollege, setEditingCollege] = useState<College | null>(null)

  // 分页状态
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  const [total, setTotal] = useState(0)
  const [totalPages, setTotalPages] = useState(0)

  // 获取学院列表
  const fetchColleges = async (page = currentPage, size = pageSize, keyword = searchTerm, schoolId = selectedSchool) => {
    try {
      setLoading(true)
      const params: any = { page, size }

      if (keyword) {
        params.keyword = keyword
      }
      if (schoolId) {
        params.schoolId = parseInt(schoolId)
      }

      const response = await adminApi.colleges.getList(params)
      if (response.data.success) {
        const pageData = response.data.data
        if (pageData.records) {
          setColleges(Array.isArray(pageData.records) ? pageData.records : [])
          setTotal(pageData.total || 0)
          setTotalPages(pageData.pages || 0)
          setCurrentPage(pageData.current || 1)
        } else {
          setColleges(Array.isArray(pageData) ? pageData : [])
        }
      }
    } catch (error) {
      console.error('获取学院列表失败:', error)
      setColleges([])
    } finally {
      setLoading(false)
    }
  }

  // 获取学校列表
  const fetchSchools = async () => {
    try {
      const response = await adminApi.schools.getList({ page: 1, size: 100 })
      if (response.data.success) {
        // 管理员接口返回的是分页数据
        setSchools(response.data.data.records || response.data.data)
      }
    } catch (error) {
      console.error('获取学校列表失败:', error)
    }
  }

  useEffect(() => {
    fetchColleges()
    fetchSchools()
  }, [])

  // 过滤学院列表
  const filteredColleges = colleges.filter(college => {
    const matchesSearch = college.collegeName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (college.schoolName && college.schoolName.toLowerCase().includes(searchTerm.toLowerCase()))
    const matchesSchool = !selectedSchool || college.schoolId.toString() === selectedSchool
    return matchesSearch && matchesSchool
  })

  // 删除学院
  const handleDelete = async (id: number) => {
    if (confirm('确定要删除这个学院吗？此操作不可撤销。')) {
      try {
        const response = await adminApi.colleges.delete(id)
        if (response.data.success) {
          // 成功删除后刷新列表
          fetchColleges()
        } else {
          alert(`删除失败: ${response.data.message}`)
        }
      } catch (error) {
        console.error('删除学院失败:', error)
        alert('删除学院时发生错误')
      }
    }
  }

  // 保存编辑后的学院信息
  const handleSaveCollege = (updatedCollege: College) => {
    // 更新本地状态
    setColleges(prevColleges => 
      prevColleges.map(college => 
        college.id === updatedCollege.id ? updatedCollege : college
      )
    )
    setEditingCollege(null)
    // 显示成功消息
    alert('学院信息更新成功')
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">加载中...</div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 页面头部 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">学院管理</h1>
          <p className="text-gray-600 mt-1">管理系统中的所有学院信息</p>
        </div>
        <Button 
          onClick={() => setShowAddModal(true)}
          className="bg-blue-600 hover:bg-blue-700"
        >
          <Plus className="h-4 w-4 mr-2" />
          添加学院
        </Button>
      </div>

      {/* 搜索和筛选栏 */}
      <Card>
        <CardContent className="pt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="搜索学院或学校名称..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <select
              value={selectedSchool}
              onChange={(e) => {
                setSelectedSchool(e.target.value)
                fetchColleges(currentPage, pageSize, searchTerm, e.target.value)
              }}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">所有学校</option>
              {schools.map((school) => (
                <option key={school.id} value={school.id.toString()}>
                  {school.schoolName}
                </option>
              ))}
            </select>
          </div>
        </CardContent>
      </Card>

      {/* 统计信息 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">学院总数</CardTitle>
            <Building2 className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{colleges.length}</div>
            <p className="text-xs text-gray-500">已注册学院</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">专业总数</CardTitle>
            <School className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {colleges.reduce((sum, college) => sum + (college.majorCount || 0), 0)}
            </div>
            <p className="text-xs text-gray-500">所有学院的专业数</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">筛选结果</CardTitle>
            <Search className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{filteredColleges.length}</div>
            <p className="text-xs text-gray-500">符合条件的学院</p>
          </CardContent>
        </Card>
      </div>

      {/* 学院列表 */}
      <Card>
        <CardHeader>
          <CardTitle>学院列表</CardTitle>
          <CardDescription>
            共 {filteredColleges.length} 个学院
          </CardDescription>
        </CardHeader>
        <CardContent>
          {filteredColleges.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              {searchTerm || selectedSchool ? '没有找到匹配的学院' : '暂无学院数据'}
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-3 px-4 font-medium text-gray-900">学院名称</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">所属学校</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">专业数量</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">创建时间</th>
                    <th className="text-right py-3 px-4 font-medium text-gray-900">操作</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredColleges.map((college) => (
                    <tr key={college.id} className="border-b hover:bg-gray-50">
                      <td className="py-3 px-4">
                        <div className="font-medium text-gray-900">{college.collegeName}</div>
                      </td>
                      <td className="py-3 px-4 text-gray-600">
                        {college.schoolName || '未知学校'}
                      </td>
                      <td className="py-3 px-4 text-gray-600">
                        {college.majorCount || 0} 个专业
                      </td>
                      <td className="py-3 px-4 text-gray-600">
                        {formatDate(college.createTime)}
                      </td>
                      <td className="py-3 px-4">
                        <div className="flex items-center justify-end space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setEditingCollege(college)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDelete(college.id)}
                            className="text-red-600 hover:text-red-700 hover:border-red-300"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 添加学院模态框 - 占位 */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-medium mb-4">添加学院</h3>
            <p className="text-gray-600 mb-4">添加学院功能正在开发中...</p>
            <div className="flex justify-end space-x-2">
              <Button
                variant="outline"
                onClick={() => setShowAddModal(false)}
              >
                取消
              </Button>
              <Button onClick={() => setShowAddModal(false)}>
                确定
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* 编辑学院模态框 */}
      {editingCollege && (
        <EditCollegeForm
          college={editingCollege}
          schools={schools}
          onSave={handleSaveCollege}
          onCancel={() => setEditingCollege(null)}
        />
      )}
    </div>
  )
}