'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Plus, Search, Edit, Trash2, GraduationCap, Building2 } from 'lucide-react'
import { Major, College, School } from '@/types'
import { majorApi, collegeApi, schoolApi, adminApi } from '@/lib/api'
import EditMajorForm from '@/components/admin/EditMajorForm'

export default function MajorsManagement() {
  const [majors, setMajors] = useState<Major[]>([])
  const [colleges, setColleges] = useState<College[]>([])
  const [schools, setSchools] = useState<School[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCollege, setSelectedCollege] = useState<string>('')
  const [selectedSchool, setSelectedSchool] = useState<string>('')
  const [showAddModal, setShowAddModal] = useState(false)
  const [editingMajor, setEditingMajor] = useState<Major | null>(null)

  // 分页状态
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  const [total, setTotal] = useState(0)
  const [totalPages, setTotalPages] = useState(0)

  // 获取专业列表
  const fetchMajors = async (page = currentPage, size = pageSize, keyword = searchTerm, collegeId = selectedCollege, schoolId = selectedSchool) => {
    try {
      setLoading(true)
      const params: any = { page, size }

      if (keyword) {
        params.keyword = keyword
      }
      if (collegeId) {
        params.collegeId = parseInt(collegeId)
      }
      if (schoolId) {
        params.schoolId = parseInt(schoolId)
      }

      const response = await adminApi.majors.getList(params)
      if (response.data.success) {
        const pageData = response.data.data
        if (pageData.records) {
          setMajors(Array.isArray(pageData.records) ? pageData.records : [])
          setTotal(pageData.total || 0)
          setTotalPages(pageData.pages || 0)
          setCurrentPage(pageData.current || 1)
        } else {
          setMajors(Array.isArray(pageData) ? pageData : [])
        }
      }
    } catch (error) {
      console.error('获取专业列表失败:', error)
      setMajors([])
    } finally {
      setLoading(false)
    }
  }

  // 获取学院列表
  const fetchColleges = async () => {
    try {
      const response = await adminApi.colleges.getList({ page: 1, size: 1000 })
      if (response.data.success) {
        setColleges(response.data.data.records || response.data.data)
      }
    } catch (error) {
      console.error('获取学院列表失败:', error)
    }
  }

  // 获取学校列表
  const fetchSchools = async () => {
    try {
      const response = await adminApi.schools.getList({ page: 1, size: 1000 })
      if (response.data.success) {
        setSchools(response.data.data.records || response.data.data)
      }
    } catch (error) {
      console.error('获取学校列表失败:', error)
    }
  }

  useEffect(() => {
    fetchColleges()
    fetchSchools()
  }, [])

  useEffect(() => {
    fetchMajors()
  }, [currentPage, pageSize])

  useEffect(() => {
    if (currentPage !== 1) {
      setCurrentPage(1)
    } else {
      fetchMajors(1, pageSize, searchTerm, selectedCollege, selectedSchool)
    }
  }, [searchTerm, selectedCollege, selectedSchool])

  // 根据选择的学校过滤学院
  const filteredColleges = selectedSchool 
    ? colleges.filter(college => college.schoolId.toString() === selectedSchool)
    : colleges

  // 分页处理函数
  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }

  const handlePageSizeChange = (size: number) => {
    setPageSize(size)
    setCurrentPage(1)
  }

  // 专业列表现在通过服务端分页，不需要客户端过滤
  const filteredMajors = Array.isArray(majors) ? majors : []

  // 删除专业
  const handleDelete = async (id: number) => {
    if (confirm('确定要删除这个专业吗？此操作不可撤销。')) {
      try {
        const response = await adminApi.majors.delete(id)
        if (response.data.success) {
          // 成功删除后刷新列表
          fetchMajors()
        } else {
          alert(`删除失败: ${response.data.message}`)
        }
      } catch (error) {
        console.error('删除专业失败:', error)
        alert('删除专业时发生错误')
      }
    }
  }

  // 保存编辑后的专业信息
  const handleSaveMajor = (updatedMajor: Major) => {
    // 更新本地状态
    setMajors(prevMajors => 
      prevMajors.map(major => 
        major.id === updatedMajor.id ? updatedMajor : major
      )
    )
    setEditingMajor(null)
    // 显示成功消息
    alert('专业信息更新成功')
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">加载中...</div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 页面头部 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">专业管理</h1>
          <p className="text-gray-600 mt-1">管理系统中的所有专业信息</p>
        </div>
        <Button 
          onClick={() => setShowAddModal(true)}
          className="bg-blue-600 hover:bg-blue-700"
        >
          <Plus className="h-4 w-4 mr-2" />
          添加专业
        </Button>
      </div>

      {/* 搜索和筛选栏 */}
      <Card>
        <CardContent className="pt-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="搜索专业名称、学院或学校..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <select
              value={selectedSchool}
              onChange={(e) => {
                setSelectedSchool(e.target.value)
                setSelectedCollege('') // 重置学院选择
              }}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">所有学校</option>
              {schools.map((school) => (
                <option key={school.id} value={school.id.toString()}>
                  {school.schoolName}
                </option>
              ))}
            </select>
            <select
              value={selectedCollege}
              onChange={(e) => setSelectedCollege(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">所有学院</option>
              {filteredColleges.map((college) => (
                <option key={college.id} value={college.id.toString()}>
                  {college.collegeName}
                </option>
              ))}
            </select>
          </div>
        </CardContent>
      </Card>

      {/* 统计信息 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">专业总数</CardTitle>
            <GraduationCap className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{majors.length}</div>
            <p className="text-xs text-gray-500">已注册专业</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">课程总数</CardTitle>
            <Building2 className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {majors.reduce((sum, major) => sum + (major.courseCount || 0), 0)}
            </div>
            <p className="text-xs text-gray-500">所有专业的课程数</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">本科专业</CardTitle>
            <GraduationCap className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {majors.filter(major => major.degreeType === '本科').length}
            </div>
            <p className="text-xs text-gray-500">本科学位专业</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">筛选结果</CardTitle>
            <Search className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{currentPage}/{totalPages}</div>
            <p className="text-xs text-gray-500">当前页/总页数</p>
          </CardContent>
        </Card>
      </div>

      {/* 专业列表 */}
      <Card>
        <CardHeader>
          <CardTitle>专业列表</CardTitle>
          <CardDescription>
            共 {total} 个专业，当前第 {currentPage} 页，每页显示 {pageSize} 条
          </CardDescription>
        </CardHeader>
        <CardContent>
          {filteredMajors.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              {searchTerm || selectedCollege ? '没有找到匹配的专业' : '暂无专业数据'}
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-3 px-4 font-medium text-gray-900">专业名称</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">所属学院</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">所属学校</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">课程数量</th>
                    <th className="text-right py-3 px-4 font-medium text-gray-900">操作</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredMajors.map((major) => (
                    <tr key={major.id} className="border-b hover:bg-gray-50">
                      <td className="py-3 px-4">
                        <div className="font-medium text-gray-900">{major.majorName}</div>
                      </td>
                      <td className="py-3 px-4 text-gray-600">
                        {major.collegeName || '未知学院'}
                      </td>
                      <td className="py-3 px-4 text-gray-600">
                        {major.schoolName || '未知学校'}
                      </td>
                      <td className="py-3 px-4 text-gray-600">
                        {major.courseCount || 0} 门课程
                      </td>
                      <td className="py-3 px-4">
                        <div className="flex items-center justify-end space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setEditingMajor(major)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDelete(major.id)}
                            className="text-red-600 hover:text-red-700 hover:border-red-300"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}

          {/* 分页控件 */}
          {total > 0 && (
            <div className="flex items-center justify-between mt-6 pt-6 border-t">
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-700">每页显示</span>
                <select
                  value={pageSize}
                  onChange={(e) => handlePageSizeChange(parseInt(e.target.value))}
                  className="px-3 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value={10}>10</option>
                  <option value={20}>20</option>
                  <option value={50}>50</option>
                  <option value={100}>100</option>
                </select>
                <span className="text-sm text-gray-700">条</span>
              </div>

              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage <= 1}
                >
                  上一页
                </Button>

                <div className="flex items-center space-x-1">
                  {Array.from({ length: Math.min(totalPages, 7) }, (_, i) => {
                    let pageNum;
                    if (totalPages <= 7) {
                      pageNum = i + 1;
                    } else if (currentPage <= 4) {
                      pageNum = i + 1;
                    } else if (currentPage >= totalPages - 3) {
                      pageNum = totalPages - 6 + i;
                    } else {
                      pageNum = currentPage - 3 + i;
                    }

                    return (
                      <Button
                        key={pageNum}
                        variant={currentPage === pageNum ? "default" : "outline"}
                        size="sm"
                        onClick={() => handlePageChange(pageNum)}
                        className="w-8 h-8 p-0"
                      >
                        {pageNum}
                      </Button>
                    );
                  })}
                </div>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage >= totalPages}
                >
                  下一页
                </Button>
              </div>

              <div className="text-sm text-gray-700">
                共 {total} 条，第 {currentPage} / {totalPages} 页
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 添加专业模态框 - 占位 */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-medium mb-4">添加专业</h3>
            <p className="text-gray-600 mb-4">添加专业功能正在开发中...</p>
            <div className="flex justify-end space-x-2">
              <Button
                variant="outline"
                onClick={() => setShowAddModal(false)}
              >
                取消
              </Button>
              <Button onClick={() => setShowAddModal(false)}>
                确定
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* 编辑专业模态框 */}
      {editingMajor && (
        <EditMajorForm
          major={editingMajor}
          colleges={colleges}
          schools={schools}
          onSave={handleSaveMajor}
          onCancel={() => setEditingMajor(null)}
        />
      )}
    </div>
  )
}