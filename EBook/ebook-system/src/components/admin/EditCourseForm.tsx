'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { adminApi } from '@/lib/api'
import { Course, Major } from '@/types'

interface EditCourseFormProps {
  course: Course
  majors: Major[]
  onSave: (updatedCourse: Course) => void
  onCancel: () => void
}

export default function EditCourseForm({ course, majors, onSave, onCancel }: EditCourseFormProps) {
  const [formData, setFormData] = useState({
    courseName: course.courseName,
    majorId: course.majorId?.toString() || '',
    referenceMaterials: course.referenceMaterials || ''
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    try {
      const requestData: any = {
        ...formData,
        majorId: formData.majorId ? parseInt(formData.majorId) : null
      }
      
      const response = await adminApi.courses.update(course.id, requestData)
      
      if (response.data.success) {
        onSave({
          ...course,
          ...formData,
          majorId: formData.majorId ? parseInt(formData.majorId) : undefined
        })
      } else {
        setError(response.data.message || '更新失败')
      }
    } catch (err) {
      console.error('更新课程失败:', err)
      setError('更新课程时发生错误')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md shadow-lg">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium">编辑课程</h3>
          <button 
            onClick={onCancel}
            className="text-gray-500 hover:text-gray-700"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </button>
        </div>
        
        {error && (
          <div className="mb-4 p-3 bg-red-50 text-red-700 rounded-md text-sm">
            {error}
          </div>
        )}
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="courseName">课程名称 *</Label>
            <Input
              id="courseName"
              name="courseName"
              value={formData.courseName}
              onChange={handleChange}
              required
            />
          </div>
          
          <div>
            <Label htmlFor="majorId">所属专业</Label>
            <select
              id="majorId"
              name="majorId"
              value={formData.majorId}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">未关联专业</option>
              {majors.map(major => (
                <option key={major.id} value={major.id.toString()}>
                  {major.majorName}
                </option>
              ))}
            </select>
          </div>
          
          <div>
            <Label htmlFor="referenceMaterials">参考资料</Label>
            <Textarea
              id="referenceMaterials"
              name="referenceMaterials"
              value={formData.referenceMaterials}
              onChange={handleChange}
              rows={3}
            />
          </div>
          
          <div className="flex justify-end space-x-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={loading}
            >
              取消
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? '保存中...' : '保存'}
            </Button>
          </div>
        </form>
      </div>
    </div>
  )
}