'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { adminApi } from '@/lib/api'
import { Major, College, School } from '@/types'

interface EditMajorFormProps {
  major: Major
  colleges: College[]
  schools: School[]
  onSave: (updatedMajor: Major) => void
  onCancel: () => void
}

export default function EditMajorForm({ major, colleges, schools, onSave, onCancel }: EditMajorFormProps) {
  const [formData, setFormData] = useState({
    majorName: major.majorName,
    collegeId: major.collegeId.toString(),
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    try {
      const response = await adminApi.majors.update(major.id, {
        ...formData,
        collegeId: parseInt(formData.collegeId)
      })
      
      if (response.data.success) {
        onSave({
          ...major,
          ...formData,
          collegeId: parseInt(formData.collegeId)
        })
      } else {
        setError(response.data.message || '更新失败')
      }
    } catch (err) {
      console.error('更新专业失败:', err)
      setError('更新专业时发生错误')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md shadow-lg">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium">编辑专业</h3>
          <button 
            onClick={onCancel}
            className="text-gray-500 hover:text-gray-700"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </button>
        </div>
        
        {error && (
          <div className="mb-4 p-3 bg-red-50 text-red-700 rounded-md text-sm">
            {error}
          </div>
        )}
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="majorName">专业名称 *</Label>
            <Input
              id="majorName"
              name="majorName"
              value={formData.majorName}
              onChange={handleChange}
              required
            />
          </div>
          
          <div>
            <Label htmlFor="collegeId">所属学院 *</Label>
            <select
              id="collegeId"
              name="collegeId"
              value={formData.collegeId}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            >
              {colleges.map(college => (
                <option key={college.id} value={college.id.toString()}>
                  {college.collegeName}
                </option>
              ))}
            </select>
          </div>
          
          <div className="flex justify-end space-x-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={loading}
            >
              取消
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? '保存中...' : '保存'}
            </Button>
          </div>
        </form>
      </div>
    </div>
  )
}